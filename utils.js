/**
 * Global reference to the app element
 * @type {HTMLElement}
 */
export const app = document.getElementById("app");

/**
 * Global application settings
 * @type {Object}
 * @property {string} theme - Current theme ('light' or 'dark')
 * @property {string} language - Current language code
 */
export const globalSettings = {
  theme: "light",
  language: "de",
};

/**
 * Error severity levels
 * @enum {string}
 */
export const ErrorLevel = {
  INFO: 'info',
  WARNING: 'warning',
  ERROR: 'error',
  CRITICAL: 'critical'
};

/**
 * Logs an error message to the console with appropriate formatting
 * @param {string} message - The error message to log
 * @param {ErrorLevel} [level=ErrorLevel.ERROR] - The severity level of the error
 * @param {Error} [originalError] - The original error object if available
 */
export const logError = (message, level = ErrorLevel.ERROR, originalError = null) => {
  const prefix = `[App ${level.toUpperCase()}]`;

  switch (level) {
    case ErrorLevel.INFO:
      console.info(`${prefix}: ${message}`);
      break;
    case ErrorLevel.WARNING:
      console.warn(`${prefix}: ${message}`);
      break;
    case ErrorLevel.ERROR:
    case ErrorLevel.CRITICAL:
      console.error(`${prefix}: ${message}`);
      if (originalError) {
        console.error('Original error:', originalError);
      }
      break;
    default:
      console.error(`${prefix}: ${message}`);
  }
};

/**
 * Safely executes a function and handles any errors that occur
 * @param {Function} fn - The function to execute
 * @param {Array} [args=[]] - Arguments to pass to the function
 * @param {string} [errorMessage='An error occurred'] - Custom error message
 * @param {ErrorLevel} [level=ErrorLevel.ERROR] - The severity level of the error
 * @returns {*} The result of the function or null if an error occurred
 */
export const trySafe = (fn, args = [], errorMessage = 'An error occurred', level = ErrorLevel.ERROR) => {
  try {
    return fn(...args);
  } catch (error) {
    logError(errorMessage, level, error);
    return null;
  }
};

/**
 * Validates user input against specified constraints
 * @param {*} value - The value to validate
 * @param {Object} options - Validation options
 * @param {boolean} [options.required=false] - Whether the value is required
 * @param {number} [options.minLength] - Minimum length for string values
 * @param {number} [options.maxLength] - Maximum length for string values
 * @param {RegExp} [options.pattern] - Regular expression pattern to match
 * @param {Function} [options.customValidator] - Custom validation function
 * @returns {Object} Validation result with isValid and message properties
 */
export const validateInput = (value, options = {}) => {
  const { required = false, minLength, maxLength, pattern, customValidator } = options;

  // Check if required
  if (required && (value === null || value === undefined || value === '')) {
    return { isValid: false, message: 'This field is required' };
  }

  // Skip other validations if value is empty and not required
  if (value === null || value === undefined || value === '') {
    return { isValid: true, message: '' };
  }

  // Check min length
  if (minLength !== undefined && typeof value === 'string' && value.length < minLength) {
    return { isValid: false, message: `Minimum length is ${minLength} characters` };
  }

  // Check max length
  if (maxLength !== undefined && typeof value === 'string' && value.length > maxLength) {
    return { isValid: false, message: `Maximum length is ${maxLength} characters` };
  }

  // Check pattern
  if (pattern && typeof value === 'string' && !pattern.test(value)) {
    return { isValid: false, message: 'Invalid format' };
  }

  // Run custom validator if provided
  if (customValidator && typeof customValidator === 'function') {
    const customResult = customValidator(value);
    if (customResult !== true) {
      return { isValid: false, message: customResult || 'Invalid value' };
    }
  }

  return { isValid: true, message: '' };
};

/**
 * Zentraler App-Speicher mit Persistenz
 * Verwaltet alle App-Daten und speichert sie im localStorage
 */
export const appStorage = {
  // Interner Speicher
  _data: {
    // Benutzerdaten
    user: {
      name: null,
    },
    // Verbundene Apps
    connectedApps: {},
    // Challenge-Daten
    challenges: {},
    // Aktive Gesundheitsziele
    activeHealthGoals: {
      fitUmgebung: false
    }
  },

  /**
   * Initialisiert den Speicher beim App-Start
   */
  init() {
    try {
      // Versuche, gespeicherte Daten aus dem localStorage zu laden
      const savedData = localStorage.getItem('appData');
      if (savedData) {
        this._data = JSON.parse(savedData);
        console.log('App-Daten aus localStorage geladen');
      }
    } catch (error) {
      logError('Fehler beim Laden der App-Daten', ErrorLevel.WARNING, error);
    }
    
    // Stelle sicher, dass alle erforderlichen Objekte existieren
    this._ensureDataStructure();
    
    // Mache appStorage global verfügbar für Debugging
    window.appStorage = this;
  },

  /**
   * Stellt sicher, dass alle erforderlichen Datenstrukturen existieren
   * @private
   */
  _ensureDataStructure() {
    if (!this._data.user) this._data.user = { name: null };
    if (!this._data.connectedApps) this._data.connectedApps = {};
    if (!this._data.challenges) this._data.challenges = {};
    if (!this._data.activeHealthGoals) this._data.activeHealthGoals = { fitUmgebung: false };
  },

  /**
   * Speichert Änderungen im localStorage
   * @private
   */
  _save() {
    try {
      localStorage.setItem('appData', JSON.stringify(this._data));
    } catch (error) {
      logError('Fehler beim Speichern der App-Daten', ErrorLevel.WARNING, error);
    }
  },

  /**
   * Getter für Benutzername
   */
  get userName() {
    return this._data.user.name || "Nutzer";
  },

  /**
   * Setter für Benutzername
   */
  set userName(name) {
    this._data.user.name = name;
    this._save();
  },

  /**
   * Getter für verbundene Apps
   */
  get connectedApps() {
    return this._data.connectedApps;
  },

  /**
   * Getter für Challenges
   */
  get challenges() {
    return this._data.challenges;
  },

  /**
   * Getter für aktive Gesundheitsziele
   */
  get activeHealthGoals() {
    return this._data.activeHealthGoals;
  },

  /**
   * Verbindet oder trennt eine Fitness-App
   * @param {string} appName - Name der App
   * @param {boolean} isConnected - Verbindungsstatus
   */
  setAppConnection(appName, isConnected) {
    this._data.connectedApps[appName] = isConnected;
    this._save();
  },

  /**
   * Aktiviert ein Gesundheitsziel
   * @param {string} goalName - Name des Ziels
   */
  activateHealthGoal(goalName) {
    if (this._data.activeHealthGoals.hasOwnProperty(goalName)) {
      this._data.activeHealthGoals[goalName] = true;
      this._save();
    }
  },

  /**
   * Setzt oder aktualisiert Challenge-Daten
   * @param {string} challengeName - Name der Challenge
   * @param {Object} data - Challenge-Daten
   */
  updateChallenge(challengeName, data) {
    this._data.challenges[challengeName] = {
      ...this._data.challenges[challengeName],
      ...data
    };
    this._save();
  },

  /**
   * Löscht alle gespeicherten Daten
   */
  reset() {
    this._data = {
      user: { name: null },
      connectedApps: {},
      challenges: {},
      activeHealthGoals: { fitUmgebung: false }
    };
    this._save();
    console.log('App-Daten zurückgesetzt');
  }
};

// Behalte appState für Abwärtskompatibilität
// export const appState = appStorage._data;

/**
 * Hilfsfunktion für den Zugriff auf appStorage._data
 * @returns {Object} Die gespeicherten App-Daten
 */
export const getAppData = () => appStorage._data;
