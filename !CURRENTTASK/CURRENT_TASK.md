# Aktuelle ToDos für Feature: [ChallengeButtons deaktivieren und Healthgoalniveau bestimmen]

## Allgemeine Hinweise für die KI

- Schreibe alle neuen Komponenten in lit-html.
- Speichere alle neuen Komponenten in /webcomponents
- Nutze bereits bestehende Komponenten bevor du neue Komponenten baust in /webcomponents
- Nutze camelCase für Variablen und Komponenten.
- Nutze kebabCase für CSS-Klassen.
- Nutze vorhandene CSS-Variablen von /variables.css
- Nutze die Schriftarten von /fonts.css
- Nutze die allgemeinen CSS Klassen von /styles.css und für Komponenten immer eine eigene CSS-Datei
- Berücksichtige bestehende Hilfsfunktionen aus /helpers
- Füge Kommentare hinzu, wenn Logik nicht selbsterklärend ist.
- Entferne ungenutzte Variablen und Imports aus der Seite auf der du gerade arbeitest
- Lies die /readme.md

---

## Beschreibung der Aufgabe
Es gibt derzeit auf jeder Challenge-Seite in src/healthgoals/challenges einen Button "Challenge jetzt starten" ganz unten auf jeder Seite. Dieser soll zukünftig standardmäßig ausgeblendet werden und nur angezeigt werden, wenn der User das Healthgoal-Niveau bestimmt hat unter hg-niveau-poll.js. Die Bedingung hierfür könnte sein: hg_fitUmgebungTemplate aufgerufen mit isActive: true. Diese wird gesetzt wenn der User das Niveau bestimmt hat und die Seite über den Button "Mit Einsteiger-Nieveau fortfahren" verlässt. Ziel soll es sein, dass erst eine Challenge gestartet werden kann, wenn das Healthgoal-Niveau bestimmt wurde und das Gesundheitsziel gesetzt wurde über "appStorage._data.activeHealthGoals: 
Object { fitUmgebung: true }". 
Dann haben wir noch das Problem, dass derzeit nur das Einsteiger-Niveau ausgewählt wird, egal was der User im Poll beantwortet hat. Wir brauchen also eine Möglichkeit Zwischen dem Einsteiger-, dem Fortgeschrittenen- und dem Profi-Niveau zu unterscheiden. Dazu brauchen wir eine neue Variable in appStorage, die wir "healthGoalLevel" nennen. Diese Variable kann die Werte "beginner", "intermediate" und "advanced" annehmen. Diese Variable wird gesetzt, wenn der User das Niveau im Poll bestimmt hat. Die Berechnung erfolgt immer so, dass die erste Antwort im Poll immer auf das Einsteiger-Niveau zutrifft, die zweite Antwort auf das Fortgeschrittenen-Niveau und die dritte Antwort auf das Profi-Niveau.
Hat die Berechnung das Einsteiger-Niveau ergeben so zeigt der primary-button zum Testabschluss "Weiter mit Einsteiger-Niveau" und der secondary-button im accordion zeigt "Weiter mit Fortgeschrittenen-Niveau" (also so wie bisher). Hat der im Ergebnis den Status "Fortgeschritten" so zeigt der primary-button zum Testabschluss "Weiter mit Fortgeschrittenen-Niveau" und der secondary-button im accordion zeigt "Weiter mit Einsteiger-Niveau". Hat der im Ergebnis den Status "Profi" so zeigt der primary-button zum Testabschluss "Weiter mit Profi-Niveau" und der secondary-button im accordion zeigt "Weiter mit Fortgeschrittenen-Niveau".

---

## Aufgaben

1. [ ] ChallengeButtons standardmäßig nicht anzeigen in allen derzeit verfügbaren challenges unter src/healthgoals/challenges/
2. [ ] ChallengeButtons nur anzeigen, wenn das Healthgoal-Niveau bestimmt wurde und das Gesundheitsziel gesetzt wurde über "appStorage._data.activeHealthGoals: Object { fitUmgebung: true }"
3. [ ] Neue Variable "healthGoalLevel" in appStorage anlegen, die die Werte "beginner", "intermediate" und "advanced" annehmen kann
    3.1 [ ] Variable "healthGoalLevel" in appStorage anlegen
    3.2 [ ] Variable "healthGoalLevel" in appStorage setzen, wenn der User das Niveau im Poll bestimmt hat
    3.3 [ ] Variable "healthGoalLevel" in appStorage auslesen, wenn der User die Challenge-Seite aufruft (vorerst nur in console.log anzeigen)
4. [ ] Berechnung des Healthgoal-Niveaus in hg_niveau_poll.js implementieren
    4.1 [ ] Erste Antwort im Poll immer auf das Einsteiger-Niveau zutreffen
    4.2 [ ] Zweite Antwort im Poll immer auf das Fortgeschrittenen-Niveau zutreffen
    4.3 [ ] Dritte Antwort im Poll immer auf das Profi-Niveau zutreffen
    4.4 [ ] Button-Texte und Accordion-Texte in hg_niveau_poll.js anpassen, damit diese die korrekten Niveaus anzeigen
    4.5 [ ] Bei einem Gleichstand zwischen den Antworten (z.B. 2-2-1) immer das niedrigere Niveau als Ergebnis setzen
5. [ ] Dokumentation für das neue Feature ergänzen
6. [ ] Code-Review und Refactoring
