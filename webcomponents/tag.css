.tag {
  display: flex;
  align-items: center;
  align-content: flex-start;
  gap: 8px;
  align-self: stretch;
  flex-wrap: wrap;
  width: fit-content;
  border-radius: 4px;
  padding: 1px 6px;
}

.tag-icon {
  max-width: 12px;
  max-height: 12px;
}

.tag-text {
  color: var(--tag-text);
}

.tags-big > .tag {
  padding: 6px 16px 4px;
}

.tags-big > .tag > .tag-icon {
  width: 16px;
  /* Nutze CSS FIlter um schwarzes icon in --primary-brand color umzuwandeln */
  filter: brightness(0) saturate(100%) invert(26%) sepia(86%) saturate(608%)
    hue-rotate(116deg) brightness(90%) contrast(104%);
}

.tags-big {
  display: flex;
  align-items: flex-start;
  align-content: flex-start;
  gap: 10px;
  align-self: stretch;
  flex-wrap: wrap;
  width: 100%;
}
