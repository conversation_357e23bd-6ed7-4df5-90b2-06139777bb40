import { html } from "lit-html";
import "./radioPoll.css";

export const radioPoll = ({ name, options }) => html`
  <div class="radio-poll-container">
    ${options.map(
      (option, index) => html`
        <label class="radio-option">
          <input type="radio" name="${name}" />
          <span>${option}</span>
        </label>
        <!-- Füge den Divider nur zwischen den Optionen ein -->
        ${index < options.length - 1 ? html`<div class="divider"></div>` : ""}
      `
    )}
  </div>
`;
