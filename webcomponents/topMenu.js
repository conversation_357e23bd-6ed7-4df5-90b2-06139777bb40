import { html } from "lit-html";
import "./topMenu.css";

// topMenu.js
export const topMenu = ({
  backIcon,
  primaryIcon,
  menuIcon,
  onBack,
  onClose, // Neuer Parameter für Schließen-Funktion
}) => html`
  <div class="top-menu-container">
    <div class="icon-back">
      <a @click=${onBack || (() => window.history.back())}>
        <!-- Custom Event listener -->
        <img src="${backIcon}" alt="Zurück" />
      </a>
    </div>
    <div class="icon-menu">
      ${primaryIcon
        ? html`<img src="${primaryIcon}" alt="Fitness Tracker" />`
        : ""}
      ${menuIcon
        ? html`<a @click=${onClose || (() => window.history.back())}>
            <!-- Close-Button mit Event -->
            <img src="${menuIcon}" alt="Menü" />
          </a>`
        : ""}
    </div>
  </div>
`;
