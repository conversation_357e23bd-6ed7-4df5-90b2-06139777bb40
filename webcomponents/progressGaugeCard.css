.progressGaugeCard {
  display: flex;
  width: 327px;
  padding: 16px;
  align-items: center;
  gap: 16px;
  border-radius: 12px;
  background: var(--cards-progress-gauge-card-background-success, #E8F4F2);
}

.progressGaugeContainer {
  display: flex;
  width: auto;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
  flex-shrink: 0;
}

.gaugeContainer {
  width: 80px;
  height: 105px;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.contentGaugeContainer {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: flex-start;
  gap: 8px;
  flex: 1 0 0;
}

.gaugeTitle {
  align-self: stretch;
  color: var(--cards-progress-gauge-card-text, #005E3F);
  font-family: "AOK Buenos Aires Text";
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 21.6px; /* 135% */
  letter-spacing: 0.15px;
  margin: 0;
}

.gaugeText {
  align-self: stretch;
  color: var(--cards-progress-gauge-card-text, #005E3F);
  font-family: "AOK Buenos Aires Text";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 18.9px; /* 135% */
  letter-spacing: 0.15px;
  margin: 0;
}

.percentage-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-family: "AOK Buenos Aires Text";
  font-size: 0.75rem;
  font-weight: 600;
  margin: 0;
  padding: 0;
  text-align: center;
  width: 100%;
  margin-top: -16px;
}

.progress-arc {
  transition: stroke-dashoffset 0.5s ease-in-out;
}
