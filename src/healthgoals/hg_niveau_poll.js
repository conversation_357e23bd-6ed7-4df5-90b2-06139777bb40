import { html } from "lit-html";
/* <PERSON><PERSON> wird hier <PERSON><PERSON><PERSON><PERSON>, da wir nicht die komplette Seite neu laden über den Router sondern nur die Inhalte aktualisieren wollen, nach jedem step */
import { render } from "lit-html";
import { progressBar } from "../../webcomponents/progressBar.js";
import { buttonStandard } from "../../webcomponents/buttonStandard.js";
import { radioPoll } from "../../webcomponents/radioPoll.js";
import { topMenu } from "../../webcomponents/topMenu.js";
import iconArrowLeft from "../../svg/icons/ArrowLeft.svg";
import iconArrowRight from "../../svg/icons/ArrowRight.svg";
import iconClose from "../../svg/icons/icon_close.svg";
import iconLift from "../../svg/icons/icon_lift.svg";
import { buttonTextIcon } from "../../webcomponents/buttonTextIcon.js";
import imgLegs from "../../img/healthgoals/poll_legs.png";
import { infoCardPlain } from "../../webcomponents/infoCardPlain.js";
import { titleDuoColor } from "../../webcomponents/titleDuoColor.js";
import { sectionParagraph } from "../../webcomponents/sectionParagraph.js";
import { accordion } from "../../webcomponents/accordion.js";
import { appStorage } from "../../utils.js";
import { router } from "../../router.js"; // Router importieren

/*
  ====================
  STATE MANAGEMENT
  ====================
*/
let currentStep = 1; // Modulweiter State für aktuellen Schritt
let isAccordionOpen = false; // State für das Accordion

/*
  ====================
  KONSTANTEN & KONFIGURATION
  ====================
*/
const steps = [
  {
    id: 1,
    title: "An wie vielen Tagen bist Du aktuell aktiv?",
    options: [
      "1 bis 2 Tage pro Woche",
      "3 bis 5 Tage pro Woche",
      "6 bis 7 Tage pro Woche",
    ],
  },
  {
    id: 2,
    title: "3-Minuten-Stufentest",
    description:
      "Führe jetzt diese Übung 3 Minuten aus. Miss bitte direkt im Anschluss Deinen Puls.",
  },
  {
    id: 3,
    title: "Wie anstrengend war der Test?",
    options: [
      "Wenig anstrengend",
      "Mittelmäßig anstrengend",
      "Sehr anstrengend",
    ],
  },
];

/*
  ====================
  HELFERFUNKTIONEN
  ====================
*/
const renderProgressBar = (step) => {
  const completedChallenges = step;
  const totalChallenges = steps.length;
  return progressBar({
    completedChallenges,
    totalChallenges,
    type: "poll",
  });
};

/* Top Menu */
const handleBack = () => {
  if (currentStep > 1) {
    currentStep = currentStep > steps.length ? steps.length : currentStep - 1;
    updateUI();
  } else {
    window.history.back(); // Navigation je nachdem ob Schritt 2 schon erreicht wurde
  }
};

const handleClose = () => {
  window.history.back(); // Direktes Zurücknavigieren ohne Schrittwechsel
};

const templateTopMenu = () => html`
  ${topMenu({
    backIcon: iconArrowLeft,
    primaryIcon: "",
    menuIcon: iconClose,
    onBack: handleBack,
    onClose: handleClose, // Schließen-Funktion
  })}
`;

/*
  ====================
  EVENT-HANDLING
  ====================
*/
// Toggle-Funktion für das Accordion
const toggleAccordion = () => {
  isAccordionOpen = !isAccordionOpen;
  updateUI();
};

const handleNextStep = () => {
  if (currentStep <= steps.length) {
    // Erlaube currentStep > steps.length für finale Auswertung
    currentStep += 1;
    updateUI();
  }
};

const handleRadioChange = () => {
  const buttonElement = document.getElementById(`button-step-${currentStep}`);
  if (buttonElement) {
    buttonElement.removeAttribute("disabled");
    buttonElement.classList.remove("disabled");
  }
};

const setupEventListeners = () => {
  if (currentStep > steps.length) {
    // Event-Listener für Abschluss-Button
    const completeButton = document.getElementById("button-complete");
    if (completeButton) {
      completeButton.addEventListener("click", () => {
        // Aktiviere das Ziel im App-State
        appStorage.activateHealthGoal("fitUmgebung");
        console.log("Button geklickt, navigiere zu /healthgoals-overview/hg-fitUmgebung");
        
        // Verwende den Router statt window.location.href
        router.navigate("/healthgoals-overview/hg-fitUmgebung");
      });
    } else {
      console.error("Button 'button-complete' nicht gefunden!");
    }
    return;
  }
  const radioButtons = document.querySelectorAll(
    `input[name="step-${currentStep}"]`
  );
  const buttonElement = document.getElementById(`button-step-${currentStep}`);

  // Radio-Buttons
  if (radioButtons) {
    radioButtons.forEach((radio) => {
      radio.addEventListener("change", handleRadioChange);
    });
  }

  // Weiter-Button
  if (buttonElement) {
    buttonElement.addEventListener("click", handleNextStep);
  }
};

const cleanupEventListeners = () => {
  const radioButtons = document.querySelectorAll(
    `input[name="step-${currentStep}"]`
  );
  const buttonElement = document.getElementById(`button-step-${currentStep}`);

  // Alte Event-Listener entfernen
  if (radioButtons) {
    radioButtons.forEach((radio) => {
      radio.removeEventListener("change", handleRadioChange);
    });
  }

  if (buttonElement) {
    buttonElement.removeEventListener("click", handleNextStep);
  }
};

/*
  ====================
  RENDER-LOGIK
  ====================
*/

// Aktualisierte renderStepContent
const renderStepContent = () => {
  if (currentStep > steps.length) {
    // Abschlussseite
    return html`
      <!-- Top Menu -->
      ${templateTopMenu()}
      <!-- Content -->
      <div class="niveau-poll-container content-top-padding">
        <div class="content-padding standard-container content-left-align">
          ${progressBar({
            completedChallenges: steps.length,
            totalChallenges: steps.length,
            type: "poll",
          })}
        </div>

        <!-- Überschrift -->
        <div class="content-padding">
          ${titleDuoColor("Dein individuelles", "Leistungsniveau")}
        </div>

        <!-- Info Card -->
        <div class="content-padding">
          ${infoCardPlain({
            icon: iconLift,
            caption: "Einsteiger-Niveau – Ich rate Dir damit zu starten.",
            textColor: "var(--caption-green)",
            backgroundColor: "var(--info-card-background-green)",
          })}
        </div>

        <!-- Paragraph -->
        ${sectionParagraph(
          'Großartig, Dein erster großer Schritt hin zu einem gesünderen "Ich" ist fast geschafft! Um diesen Schritt abzuschließen, bestätige bitte Dein Leistungsniveau:'
        )}

        <!-- Akkordion -->
        ${accordion({
          title: "Anderes Leistungsniveau wählen",
          text: "Wenn Du möchtest, kannst Du auch ein anderes Leistungsniveau wählen. Bitte bedenke, dass es dadurch schwieriger für Dich werden könnte, Dein Gesundheitsziel zu erreichen.",
          buttonLabel: "Weiter mit Fortgeschrittenen-Niveau",
          buttonUrl: "/hg-niveau-poll-advanced", // Deine gewünschte Navigo-URL
          badge: null, // oder badge: "1", falls du eins möchtest
          open: isAccordionOpen,
          onToggle: toggleAccordion,
        })}

        <!-- Button -->
        <div class="button-container content-padding button-bottom">
          ${buttonStandard({
            text: "Weiter mit Einsteiger-Niveau",
            variant: "primary",
            id: "button-complete",
          })}
        </div>
      </div>
    `;
  }
  const step = steps.find((s) => s.id === currentStep);

  if (!step) {
    console.error(`Step ${currentStep} not found!`);
    return html`<p>Ein Fehler ist aufgetreten. Bitte lade die Seite neu.</p>`;
  }

  return html`
    <!-- Top Menu -->
    ${templateTopMenu()}
    <!-- Content -->
    <div class="niveau-poll-container content-top-padding">
      <div class="content-padding standard-container content-left-align">
        ${renderProgressBar(currentStep)}
      </div>
      <!-- Überschrift -->
      <h2 class="step-title green-text content-padding">${step.title}</h2>
      <!-- Poll -->
      ${step.description
        ? html`
            <p
              class="instruction-text ${currentStep === 2
                ? "green-text content-padding"
                : ""}"
            >
              ${step.description}
            </p>
            <!-- Button für Step 2 -->
            ${currentStep === 2
              ? html`<div class="content-padding">
                  ${buttonTextIcon(
                    "Zur Übungsanleitung",
                    iconArrowRight,
                    "right"
                  )}
                </div>`
              : ""}
            <!-- Bild nur für Step 2 -->
            ${currentStep === 2
              ? html`<img src="${imgLegs}" alt="Beine" class="poll-image" />`
              : ""}
          `
        : radioPoll({ name: `step-${currentStep}`, options: step.options })}
      <!-- Zusatztext in Step 1-->
      ${currentStep === 1
        ? html`<div class="content-padding content-top-padding">
            ${buttonTextIcon(
              'Was bedeutet "aktiv" sein?',
              iconArrowRight,
              "right"
            )}
          </div>`
        : ""}
      <!-- Button -->
      <div class="button-container content-padding button-bottom">
        ${buttonStandard({
          text:
            currentStep === steps.length
              ? "Abschließen"
              : "Zum nächsten Schritt",
          variant: "primary",
          disabled: true,
          id: `button-step-${currentStep}`,
        })}
      </div>
    </div>
  `;
};

const updateUI = () => {
  cleanupEventListeners(); // Alte Listener entfernen
  
  // Finde den content-wrapper innerhalb des page-content
  const contentWrapper = document.querySelector('.page-content .app-content-wrapper');
  if (contentWrapper) {
    render(renderStepContent(), contentWrapper);
    setupEventListeners(); // Neue Listener hinzufügen
  } else {
    console.error("Content wrapper not found!");
  }
};

/*
  ====================
  ÖFFENTLICHE SCHNITTSTELLE
  ====================
*/
export const templateNiveauPoll = () => {
  // Initialen State zurücksetzen beim ersten Aufruf
  currentStep = 1;
  
  // Verzögertes Rendering, damit der Router zuerst die Seite wechseln kann
  setTimeout(() => {
    updateUI();
  }, 0);
  
  // Leeres Template zurückgeben, damit der Router die Struktur erstellt
  return html``;
};
