import { html } from "lit-html";
import { unsafeHTML } from "lit-html/directives/unsafe-html.js";
import { topMenu } from "../../../webcomponents/topMenu.js";
import iconArrowLeft from "../../../svg/icons/ArrowLeft.svg";
import iconDotMenu from "../../../svg/icons/PointsVertical.svg";
import iconInfo from "../../../svg/icons/icon_info.svg";
import iconTimelater from "../../../svg/icons/icon_timelater.svg";
import iconLink from "../../../svg/icons/icon_link.svg";
import iconLinkExternal from "../../../svg/icons/icon_linkExternal.svg";
import iconGift from "../../../svg/icons/icon_gift.svg";
import iconTagSport from "../../../svg/icons/icon_tag_sport.svg";
import { router, exitWithSlide } from "../../../router.js";
import { buttonTextIcon } from "../../../webcomponents/buttonTextIcon.js";
import { buttonStandard } from "../../../webcomponents/buttonStandard.js";
import { infoCardBullets } from "../../../webcomponents/infoCardBullets.js";
import { infoCardPlain } from "../../../webcomponents/infoCardPlain.js";
import { createSegmentedControl } from "../../../webcomponents/segmentedControl.js";
import { pillsContainer } from "../../../webcomponents/pills.js";
import iconChevronRight from "../../../svg/icons/icon_chevron_right.svg";
import { listItem } from "../../../webcomponents/listItem.js";
import { appStorage, getAppData } from "../../../utils.js";
import { sectionTitle } from "../../../webcomponents/sectionTitle.js";
import { imageCardNarrow } from "../../../webcomponents/imageCardNarrow.js";
import imgLockereWanderung from "../../../img/healthgoals/hg_lockereWanderung.jpg";
import { refreshDragScroll } from "../../../helpers/dragScroll.js";
import { showDialog } from "../../../helpers/dialog-helper.js";
import { progressGaugeCard } from "../../../webcomponents/progressGaugeCard.js";

/**
 * Top Menu Template mit Slide-Out-Animation beim Zurück-Button
 * @returns {TemplateResult} Das Top-Menu Template
 */
const templateTopMenu = () => {
  // Bestimme das Zurück-Ziel basierend auf dem aktiven Status des Gesundheitsziels
  const backTarget = appStorage._data.activeHealthGoals.fitUmgebung
    ? "/healthgoals-overview/hg-fitUmgebung-active"
    : "/healthgoals-overview/hg-fitUmgebung";

  return html`
    ${topMenu({
      backIcon: iconArrowLeft,
      primaryIcon: "",
      menuIcon: iconDotMenu,
      onBack: () => exitWithSlide(backTarget)
    })}
  `;
};

/**
 * List Item Content
 */
const trainingsplan = [
  {
    text: "1. Training: 7 - 10 km locker wandern",
    icon: iconChevronRight,
    iconPosition: "right",
    onClick: () => router.navigate("/training/lockere-wanderung")
  },
  {
    text: "2. Training: 7 - 10 km locker wandern",
    icon: iconChevronRight,
    iconPosition: "right",
    onClick: () => router.navigate("/training/lockere-wanderung")
  }
];

/**
 * Segmented Control für die Tabs "Übung" und "Trainingsplan"
 */
const createSegments = (isStarted) => [
  {
    id: "uebung",
    title: "Übung",
    content: html`
      <div class="tabpadding black-text">
        <!-- Beschreibungstext oder Trainings-Bereich je nach Status -->
        ${isStarted ? html`
          <!-- Wenn Challenge gestartet ist -->
          ${sectionTitle("Trainings")}
          <p class="challenge-description content-padding">
            Ich empfehle Dir Routen bei komoot, die genau passend für diese Challenge sind.
          </p>
          
          <div class="content-padding content-no-bottom-padding content-no-top-padding">
            ${buttonTextIcon("Passende komoot-Routen zeigen", iconLinkExternal, "right")}
          </div>
        ` : html`
          <!-- Wenn Challenge noch nicht gestartet ist -->
          <p class="challenge-description content-padding">
            In malerischer Natur ist nicht nur der Alltagsstress weit weg. Wandern trainiert auch Deinen Körper und kurbelt Deinen Kalorienverbrauch an.
          </p>
          
          <!-- Grüne Info-Card mit Bullet Points - nur anzeigen wenn Challenge nicht gestartet ist -->
          <div class="standard-container content-padding">
            ${infoCardBullets({
              title: "Was wirst Du erreichen?",
              bulletPoints: [
                "Du stärkst Dein Herz-Kreislauf-System.",
                "Du verbringst mehr Zeit in der Natur.",
                "Du unterstützt Dein Immunsystem.",
                "Du lernst Deine Umgebung besser kennen."
              ],
              background: "--info-card-background-green"
            })}
          </div>
        `}
        
        <!-- Komoot Premium Gutschein - nur anzeigen wenn Challenge aktiv -->
        ${isStarted ? html`
          <div class="standard-container content-padding">
            ${infoCardPlain({
              icon: iconGift,
              title: "Ich schenke Dir komoot Premium",
              text: "Jetzt Gutschein einlösen und \"komoot\" Premium für 90 Tage nutzen (exklusiv für AOK PLUS-Versicherte – endet automatisch).",
              linkText: "Gutschein einlösen",
              backgroundColor: "var(--info-card-background-yellow)"
            })}
          </div>
        ` : ''}

        ${isStarted ? '' : html`
          ${sectionTitle("Diese Übungen erwarten Dich")}
        `}
        <!-- Image Cards -->
        <div class="image-card-narrow-scroll-container">
          <div class="standard-container content-padding image-card-narrow-container">
            ${imageCardNarrow({
              imgPath: imgLockereWanderung,
              title: "Lockere Wanderung",
              altText: "Lockere Wanderung",
              link: "/training/lockere-wanderung"
            })}
            ${imageCardNarrow({
              imgPath: imgLockereWanderung,
              title: "Lockere Wanderung",
              altText: "Lockere Wanderung",
              link: "/training/lockere-wanderung"
            })}
          </div>
        </div>

        <!-- Gelbe Info-Card mit Vorsichtsmaßnahmen, nur anzeigen wenn Challenge nicht gestartet ist -->
        ${isStarted ? '' : html`
        <div class="standard-container content-padding">
          ${infoCardPlain({
            title: "Vorsichtsmaßnahmen",
            text: unsafeHTML(`In den folgenden Fällen solltest Du bitte keine Wander-Übung machen:
            <ul>
              <li>Wenn Du eine Bein- oder Fußverletzung hast.</li>
              <li>Wenn Du bei der Übung Schmerzen in den Beinen verspürst.</li>
              <li>Wenn Du schwanger bist.</li>
            </ul>
            <p><strong>Hinweise:</strong> Halte während der Übung bitte nicht den Atem an. Wende Dich an einen Arzt, wenn Du bei der Durchführung dieser Übung einen der oben genannten Schmerzen verspürst.</p>`),
            backgroundColor: "var(--info-card-background-yellow)"
          })}
        </div>
        `}

      </div>
    `
  },
  {
    id: "trainingsplan",
    title: "Trainingsplan",
    content: html`
      <div class="tabpadding">
        <div class="content-padding content-no-top-padding">
          ${listItem(trainingsplan)}
        </div>
      </div>
    `
  }
];

// Entferne diese Zeile, da wir die Segmente jetzt dynamisch erstellen
// const segmentedControl = createSegmentedControl(segments);

/**
 * Zeigt den Dialog zur App-Verbindung an
 */
const showConnectionDialog = () => {
  showDialog({
    title: "Verbinde bitte Deine Fitness-App",
    text: unsafeHTML("<p>Wähle mindestens eine der folgenden Verbindungen aus, bevor Du mit dieser Challenge startest.</p> <p>Verbinde Dich entweder mit der komoot-App und lass' Dich darüber navigieren oder verbinde Dein Fitness-Armband und starte eine Aktivität.</p> <p>Für die Bonifizierung prüft die NAVIDA-App Deine Trainingsdaten sobald Du den Button \"Training erfassen\" drückst.</p>"),
    icon: iconLink,
    iconColor: "var(--bubble-yellow)", // Gelbe Farbe für die Bubble
    iconCorner: "top-left", // Spitze Ecke oben links
    buttonPrimary: {
      text: "Weiter",
      link: "/tracker-connect"
    },
    buttonSecondary: {
      text: "Abbrechen"
    }
  });
};

/**
 * Zeigt den Dialog zur Trainingserfassung an
 */
const showTrainingRecordDialog = () => {
  showDialog({
    title: "Wann hast Du trainiert?",
    text: unsafeHTML("Toll, dass Du Deine Tageschallenge erfolgreich durchgeführt hast.<br>Nach Deiner Bestätigung wird überprüft, ob Dein Fitness-Tracker etwas aufgezeichnet hat und Du ausreichend trainiert hast. Das kann bis zu zwei Tage dauern."),
    icon: iconTagSport,
    iconColor: "var(--bubble-yellow)",
    iconCorner: "top-left",
    buttonPrimary: {
      text: "Ich habe heute trainiert",
      onClick: () => {
        // Hier Logik für die Trainingserfassung einfügen
        console.log("Training wurde erfasst");
        
        // Optional: Aktualisiere den Challenge-Status
        if (appStorage._data.challenges && appStorage._data.challenges.lockereWanderung) {
          appStorage._data.challenges.lockereWanderung.completedTrainings += 1;
          // Seite neu rendern, um den aktualisierten Status anzuzeigen
          router.navigate("/challenge/lockere-wanderung");
        }
      }
    },
    buttonSecondary: {
      text: "Abbrechen"
    }
  });
};

/**
 * Template für die "Lockere Wanderung" Challenge-Seite
 * @returns {TemplateResult} Das Challenge-Seiten Template
 */
export const templateLockereWanderung = () => {
  // Nach dem Rendern das Drag-Scrolling initialisieren
  setTimeout(() => {
    refreshDragScroll('.image-card-narrow-container');
  }, 100);

  // Prüfen, ob die Challenge bereits gestartet wurde
  const appData = getAppData();
  const challengeStarted = appData.challenges && 
                          appData.challenges.lockereWanderung && 
                          appData.challenges.lockereWanderung.started;
  
  // Daten für die Gauge-Komponente
  const completedTrainings = challengeStarted ? appStorage._data.challenges.lockereWanderung.completedTrainings : 0;
  const totalTrainings = challengeStarted ? appStorage._data.challenges.lockereWanderung.totalTrainings : 2;

  // Erstelle die Segmente mit dem aktuellen Status
  const segments = createSegments(challengeStarted);
  const segmentedControl = createSegmentedControl(segments);

  return html`
    ${templateTopMenu()}
    <div class="content-left-align content-top-padding">
      <h2 class="content-no-bottom-margin content-padding">
        <span class="dark-grn-text">Lockere Wanderung</span>
      </h2>

      <!-- Pills Container -->
      <div class="content-padding">
        ${challengeStarted ? 
          pillsContainer([
            { text: "Gerade Aktiv", color: "--pill-accent-blue-background", textColor: "--pill-accent-blue-text" }
          ]) :
          pillsContainer([
            { text: "7 bis 10 km", color: "--pill-green-background", textColor: "--pill-green-text" },
            { text: "14 Tage", color: "--pill-green-background", textColor: "--pill-green-text", iconName: iconTimelater, iconPosition: "left" },
            { text: "auch ohne Fitness-Armband möglich", color: "--pill-green-background", textColor: "--pill-green-text", iconName: iconInfo, iconPosition: "right" }
          ])
        }
      </div>

      <!-- Progress Gauge Card - nur anzeigen, wenn die Challenge gestartet wurde -->
      ${challengeStarted ? html`
        <div class="content-padding">
          ${progressGaugeCard({
            title: "Das steht als nächstes an:",
            description: "1. Training: 7-10 km locker wandern",
            completedTrainings: completedTrainings,
            totalTrainings: totalTrainings
          })}
        </div>
      ` : ''}

      <!-- Segmented Control (Tab Bar) -->
      <div id="segmentedControlContainer" class="standard-container">
        ${segmentedControl.template}
      </div>

      <!-- Erfahre mehr Text -->
      <p class="content-padding black-text">Erfahre hier mehr.</p>

      <!-- Info Button -->
      <div class="standard-container content-padding">
        ${buttonTextIcon("Informationen und Quellen", iconInfo, "left")}
      </div>

      <!-- Start/Erfassen Button - Text ändert sich je nach Status -->
      <div class="standard-container content-padding">
        ${buttonStandard({
          text: challengeStarted ? "Training erfassen" : "Challenge jetzt starten",
          variant: "primary",
          onClick: challengeStarted 
            ? showTrainingRecordDialog 
            : showConnectionDialog
        })}
      </div>
    </div>
  `;
};

console.log("Challenge Lockere Wanderung loaded");
