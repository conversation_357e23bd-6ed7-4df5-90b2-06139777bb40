import { html } from "lit-html";
import { unsafeHTML } from "lit-html/directives/unsafe-html.js";
import { topMenu } from "../../../webcomponents/topMenu.js";
import iconArrowLeft from "../../../svg/icons/ArrowLeft.svg";
import iconDotMenu from "../../../svg/icons/PointsVertical.svg";
import iconInfo from "../../../svg/icons/icon_info.svg";
import iconTimelater from "../../../svg/icons/icon_timelater.svg";
import { exitWithSlide } from "../../../router.js";
import { buttonTextIcon } from "../../../webcomponents/buttonTextIcon.js";
import { buttonStandard } from "../../../webcomponents/buttonStandard.js";
import { infoCardBullets } from "../../../webcomponents/infoCardBullets.js";
import { infoCardPlain } from "../../../webcomponents/infoCardPlain.js";
import { createSegmentedControl } from "../../../webcomponents/segmentedControl.js";
import { pillsContainer } from "../../../webcomponents/pills.js";
import iconChevronRight from "../../../svg/icons/icon_chevron_right.svg";
import { listItem } from "../../../webcomponents/listItem.js";
import { appStorage } from "../../../utils.js";
import { sectionTitle } from "../../../webcomponents/sectionTitle.js";
import { imageCardNarrow } from "../../../webcomponents/imageCardNarrow.js";
import imgPlogging from "../../../img/healthgoals/hg_plogging.jpg";
import { initDragScroll, refreshDragScroll } from "../../../helpers/dragScroll.js";

/**
 * Top Menu Template mit Slide-Out-Animation beim Zurück-Button
 * @returns {TemplateResult} Das Top-Menu Template
 */
const templateTopMenu = () => {
  // Bestimme das Zurück-Ziel basierend auf dem aktiven Status des Gesundheitsziels
  const backTarget = appStorage._data.activeHealthGoals.fitUmgebung
    ? "/healthgoals-overview/hg-fitUmgebung-active"
    : "/healthgoals-overview/hg-fitUmgebung";

  return html`
    ${topMenu({
      backIcon: iconArrowLeft,
      primaryIcon: "",
      menuIcon: iconDotMenu,
      onBack: () => exitWithSlide(backTarget)
    })}
  `;
};

/**
 * List Item Content
 */
const trainingsplan = [
  {
    text: "1. Training: 6 - 8 km Joggen und Müll sammeln",
    icon: iconChevronRight,
    iconPosition: "right",
    onClick: () => router.navigate("/training/plogging")
  },
  {
    text: "2. Training: 6 - 8 km Joggen und Müll sammeln",
    icon: iconChevronRight,
    iconPosition: "right",
    onClick: () => router.navigate("/training/plogging")
  },
  {
    text: "3. Training: 6 - 8 km Joggen und Müll sammeln",
    icon: iconChevronRight,
    iconPosition: "right",
    onClick: () => router.navigate("/training/plogging")
  },
  {
    text: "4. Training: 6 - 8 km Joggen und Müll sammeln",
    icon: iconChevronRight,
    iconPosition: "right",
    onClick: () => router.navigate("/training/plogging")
  }
];

/**
 * Segmented Control für die Tabs "Übung" und "Trainingsplan"
 */
const segments = [
  {
    id: "uebung",
    title: "Übung",
    content: html`
      <div class="tabpadding black-text">
        <!-- Beschreibungstext -->
        <p class="challenge-description content-padding">
          Plogging verbindet Joggen mit Müllsammeln und ist damit nicht nur gut für Deine Fitness, sondern auch für die Umwelt. Du tust etwas für Deine Gesundheit und hilfst gleichzeitig, Deine Umgebung sauberer zu machen.
        </p>

        <!-- Grüne Info-Card mit Bullet Points -->
        <div class="standard-container content-padding">
          ${infoCardBullets({
            title: "Was wirst Du erreichen?",
            bulletPoints: [
              "Du verbesserst Deine Ausdauer und Fitness.",
              "Du leistest einen aktiven Beitrag zum Umweltschutz.",
              "Du trainierst verschiedene Muskelgruppen durch das Bücken und Aufheben.",
              "Du förderst Dein Umweltbewusstsein."
            ],
            background: "--info-card-background-green"
          })}
        </div>

        ${sectionTitle("Diese Übungen erwarten Dich")}
        <!-- Image Cards -->
        <div class="image-card-narrow-scroll-container">
          <div class="standard-container content-padding image-card-narrow-container">
            ${imageCardNarrow({
              imgPath: imgPlogging,
              title: "Joggen und Müll sammeln",
              altText: "Joggen und Müll sammeln",
              link: "/training/plogging"
            })}
            ${imageCardNarrow({
              imgPath: imgPlogging,
              title: "Joggen und Müll sammeln",
              altText: "Joggen und Müll sammeln",
              link: "/training/plogging"
            })}
            ${imageCardNarrow({
              imgPath: imgPlogging,
              title: "Joggen und Müll sammeln",
              altText: "Joggen und Müll sammeln",
              link: "/training/plogging"
            })}
            ${imageCardNarrow({
              imgPath: imgPlogging,
              title: "Joggen und Müll sammeln",
              altText: "Joggen und Müll sammeln",
              link: "/training/plogging"
            })}
          </div>
        </div>

        <!-- Gelbe Info-Card mit Vorsichtsmaßnahmen -->
        <div class="standard-container content-padding">
          ${infoCardPlain({
            title: "Vorsichtsmaßnahmen",
            text: unsafeHTML(`In den folgenden Fällen solltest Du bitte keine Plogging-Übung machen:
            <ul>
              <li>Wenn Du eine Verletzung an Beinen, Rücken oder Armen hast.</li>
              <li>Wenn Du bei der Übung Schmerzen verspürst.</li>
              <li>Bei extremen Wetterbedingungen wie Gewitter oder Glatteis.</li>
            </ul>
            <p><strong>Hinweise:</strong> Trage Handschuhe zum Müllsammeln. Nimm einen Beutel für den gesammelten Müll mit. Achte auf Deine Umgebung und vermeide gefährliche Gegenstände wie Glasscherben oder Spritzen.</p>`),
            backgroundColor: "var(--info-card-background-yellow)"
          })}
        </div>
      </div>
    `
  },
  {
    id: "trainingsplan",
    title: "Trainingsplan",
    content: html`
      <div class="tabpadding">
        <div class="content-padding content-no-top-padding">
          ${listItem(trainingsplan)}
        </div>
      </div>
    `
  }
];

const segmentedControl = createSegmentedControl(segments);

/**
 * Template für die "Joggen und Müll sammeln (Plogging)" Challenge-Seite
 * @returns {TemplateResult} Das Challenge-Seiten Template
 */
export const templatePlogging = () => {
  // Healthgoal-Level für Debugging ausgeben
  console.log('Challenge Plogging - Healthgoal-Level:', appStorage._data.healthGoalLevel);
  console.log('Challenge Plogging - Healthgoal aktiv:', appStorage._data.activeHealthGoals.fitUmgebung);

  // Nach dem Rendern das Drag-Scrolling initialisieren
  setTimeout(() => {
    refreshDragScroll('.image-card-narrow-container');
  }, 100);

  return html`
    ${templateTopMenu()}
    <div class="content-left-align content-top-padding">
      <h2 class="content-no-bottom-margin content-padding">
        <span class="dark-grn-text">Joggen und Müll sammeln (Plogging)</span>
      </h2>

      <!-- Pills Container -->
      <div class="content-padding">
        ${pillsContainer([
          { text: "6 bis 8 km", color: "--pill-green-background", textColor: "--pill-green-text" },
          { text: "14 Tage", color: "--pill-green-background", textColor: "--pill-green-text", iconName: iconTimelater, iconPosition: "left" },
          { text: "auch ohne Fitness-Armband möglich", color: "--pill-green-background", textColor: "--pill-green-text", iconName: iconInfo, iconPosition: "right" }
        ])}
      </div>

      <!-- Segmented Control (Tab Bar) -->
      <div id="segmentedControlContainer" class="standard-container">
        ${segmentedControl.template}
      </div>

      <!-- Erfahre mehr Text -->
      <p class="content-padding black-text">Erfahre hier mehr.</p>

      <!-- Info Button -->
      <div class="standard-container content-padding">
        ${buttonTextIcon("Informationen und Quellen", iconInfo, "left")}
      </div>

      <!-- Start Button - nur anzeigen wenn Healthgoal aktiv und Niveau bestimmt -->
      ${appStorage._data.activeHealthGoals.fitUmgebung && appStorage._data.healthGoalLevel ? html`
        <div class="standard-container content-padding">
          ${buttonStandard({
            text: "Challenge jetzt starten",
            variant: "primary"
          })}
        </div>
      ` : html`
        <!-- Button ausgeblendet: Healthgoal-Niveau muss erst bestimmt werden -->
        <div class="standard-container content-padding">
          <p class="caption black-text">Bestimme zuerst Dein Healthgoal-Niveau, um Challenges zu starten.</p>
        </div>
      `}
    </div>
  `;
};

console.log("Challenge Plogging loaded");
