
import { html } from "lit-html";
import { unsafeHTML } from "lit-html/directives/unsafe-html.js";
import { topMenu } from "../../../webcomponents/topMenu.js";
import iconArrowLeft from "../../../svg/icons/ArrowLeft.svg";
import iconDotMenu from "../../../svg/icons/PointsVertical.svg";
import iconSmartphoneTracker from "../../../svg/icons/icon_smartphoneTracker.svg";
import { exitWithSlide } from "../../../router.js";
import { sectionTitle } from "../../../webcomponents/sectionTitle.js";
import { sectionSubtitle } from "../../../webcomponents/sectionSubtitle.js";
import { infoCardPlain } from "../../../webcomponents/infoCardPlain.js";
import { sectionBulletList } from "../../../webcomponents/sectionBulletList.js";
import { titleDuoColor } from "../../../webcomponents/titleDuoColor.js";
import imgTreppensteigen from "../../../img/healthgoals/trainings/tr_treppensteigen.jpg";
import { buttonStandard } from "../../../webcomponents/buttonStandard.js";
import { appStorage } from "../../../utils.js";

/**
 * Top Menu Template mit Slide-Out-Animation beim Zurück-Button
 * @returns {TemplateResult} Das Top-Menu Template
 */
const templateTopMenu = () => {
  // TODO: Bestimme das Zurück-Ziel basierend auf dem aktiven Status des Gesundheitsziels
  const backTarget = "/challenge/spazieren-gehen"; // Zurück zur Challenge-Seite
  
  return html`
    ${topMenu({
      backIcon: iconArrowLeft,
      primaryIcon: "",
      menuIcon: iconDotMenu,
      onBack: () => exitWithSlide(backTarget)
    })}
  `;
};

/**
 * Template für die "Treppensteigen" Trainings-Seite
 * @returns {TemplateResult} Das Trainings-Seiten Template
 */
export const templateTreppensteigen = () => {
  // Prüfen, ob ein aktives Gesundheitsziel vorhanden ist
  const hasActiveHealthGoal = appStorage._data.activeHealthGoals &&
    (appStorage._data.activeHealthGoals.fitUmgebung ||
     Object.values(appStorage._data.activeHealthGoals).some(value => value === true));
  
  return html`
    ${templateTopMenu()}
    <div class="content-left-align content-top-padding">
      <div class="content-padding center-text content-bottom-padding">
        ${titleDuoColor("Treppensteigen", "", "h1")}
        ${sectionSubtitle("10 Minuten", "dark-grn-text", true)}        
        <div class="standard-container">
          ${infoCardPlain({
            icon: iconSmartphoneTracker,
            text: "Bitte starte eine Aktivität auf Deinem Fitness-Armband oder in Deiner Fitness-App, falls es das nicht automatisch macht.",
            backgroundColor: "var(--info-card-background-yellow)"
          })}
        </div>
      </div>
    </div>
    <img src="${imgTreppensteigen}" alt="Training Treppensteigen">
    <div class="content-padding standard-container content-bottom-padding">
      ${sectionSubtitle("Details zur Übung", "dark-grn-text")}
      ${sectionBulletList([
        "Gehe in eine Liegestützposition und lege Deine Unterarme schulterbreit auf den Boden (die Ellenbogen sind auf Schulterhöhe).",
        "Strecke Deine Beine aus und stelle Dich auf die Zehenspitzen. Dann hebst Du Deinen Körper an und lässt ihn fest und gerade wie ein Brett werden.",
        "Po und Bauch sind die ganze Zeit angespannt, der Kopf ist die Verlängerung der Wirbelsäule. Der Blick ist nach unten gerichtet.",
        "Achte darauf, stabil in dieser Position zu bleiben und nicht ins Hohlkreuz zu fallen oder den Po oder Kopf nach oben durchzustrecken.",
        "Diese Position hältst Du dann mehrere Sekunden."
      ])}
    </div>
    <div class="content-padding standard-container content-bottom-padding">
        ${infoCardPlain({
          title: "Vorsichtsmaßnahmen",
          text: unsafeHTML(`In den folgenden Fällen solltest Du bitte keine Treppensteigen-Übung machen:
          <ul>
            <li>Wenn Du eine Verletzung an Beinen, Knien oder Füßen hast.</li>
            <li>Wenn Du bei der Übung Schmerzen verspürst.</li>
            <li>Wenn Du unter Schwindel oder Gleichgewichtsstörungen leidest.</li>
            <li>Bei Herz-Kreislauf-Erkrankungen ohne ärztliche Freigabe.</li>
          </ul>
          <p><strong>Hinweise:</strong> Halte Dich beim Treppensteigen am Geländer fest, wenn Du unsicher bist. Trage rutschfeste Schuhe und achte auf eine gute Beleuchtung der Treppe.</p>`),
          textColor: "white",
          backgroundColor: "var(--info-card-background-red)"
        })}
    </div>
    <div class="content-padding standard-container content-bottom-padding">
      ${hasActiveHealthGoal ? buttonStandard({
        text: "Weiter",
        variant: "primary"
      }) : ''}
    </div>
  `;
};

