import { html } from "lit-html";
import { unsafeHTML } from "lit-html/directives/unsafe-html.js";
import { topMenu } from "../../../webcomponents/topMenu.js";
import iconArrowLeft from "../../../svg/icons/ArrowLeft.svg";
import iconDotMenu from "../../../svg/icons/PointsVertical.svg";
import iconSmartphoneTracker from "../../../svg/icons/icon_smartphoneTracker.svg";
import { router, exitWithSlide } from "../../../router.js";
import { sectionTitle } from "../../../webcomponents/sectionTitle.js";
import { sectionSubtitle } from "../../../webcomponents/sectionSubtitle.js";
import { infoCardPlain } from "../../../webcomponents/infoCardPlain.js";
import { sectionParagraph } from "../../../webcomponents/sectionParagraph.js";
import { sectionBulletList } from "../../../webcomponents/sectionBulletList.js";
import { titleDuoColor } from "../../../webcomponents/titleDuoColor.js";
import imgSpazierenGehen from "../../../img/healthgoals/hg_spazierenGehen.jpg";
import { buttonStandard } from "../../../webcomponents/buttonStandard.js";
import { appStorage } from "../../../utils.js";

/**
 * Top Menu Template mit Slide-Out-Animation beim Zurück-Button
 * @returns {TemplateResult} Das Top-Menu Template
 */
const templateTopMenu = () => {
  // Zurück zur Challenge-Seite
  const backTarget = "/challenge/spazieren-gehen"; 
  
  return html`
    ${topMenu({
      backIcon: iconArrowLeft,
      primaryIcon: "",
      menuIcon: iconDotMenu,
      onBack: () => exitWithSlide(backTarget)
    })}
  `;
};

/**
 * Template für die "Spazieren gehen" Trainings-Seite
 * @returns {TemplateResult} Das Trainings-Seiten Template
 */
export const templateSpazierenGehen = () => {
  // Prüfen, ob ein aktives Gesundheitsziel vorhanden ist
  const hasActiveHealthGoal = appStorage._data.activeHealthGoals &&
    (appStorage._data.activeHealthGoals.fitUmgebung ||
     Object.values(appStorage._data.activeHealthGoals).some(value => value === true));
  
  return html`
    ${templateTopMenu()}
    <div class="content-left-align content-top-padding">
      <div class="content-padding center-text content-bottom-padding">
        ${titleDuoColor("Spazieren gehen", "", "h1")}
        ${sectionSubtitle("2 bis 4 km", "dark-grn-text", true)}        
        <div class="standard-container">
          ${infoCardPlain({
            icon: iconSmartphoneTracker,
            text: "Bitte starte eine Aktivität auf Deinem Fitness-Armband oder in Deiner Fitness-App, falls es das nicht automatisch macht.",
            backgroundColor: "var(--info-card-background-yellow)"
          })}
        </div>
      </div>
    </div>
    <img src="${imgSpazierenGehen}" alt="Training Spazieren gehen">
    <div class="content-padding standard-container content-top-padding">
      ${sectionSubtitle("Details zur Übung", "dark-grn-text")}
    </div>
      ${sectionParagraph(
        "Entdecke Deine Umgebung beim Spaziergang! Spazierengehen stärkt dein Herz-Kreislauf-System, hebt die Stimmung und bietet Dir die perfekte Gelegenheit, die Natur in vollen Zügen zu genießen."
      )}
      ${sectionParagraph(
        "Ob im Park, Wald oder durch die Stadt – ein Spaziergang kann Wunder für Dein Wohlbefinden bewirken. Regelmäßiges lockeres Gehen hilft, Stress abzubauen, die Kreativität zu fördern und die Gesundheit zu stärken. Außerdem bietet es Dir die Chance Deine Umgebung bewusst wahrzunehmen und neue Orte zu entdecken."
      )}
      ${sectionParagraph(
        "Also schnapp Dir bequeme Schuhe und entdecke Deine Umgebung Schritt für Schritt – für Dein Wohlbefinden und Deine Fitness!"
      )}

      ${sectionParagraph("Vorbereitung und Planung", true)}
      ${sectionBulletList([
        "Wähle eine Route, die zu Deiner Fitness passt",
        "Plane 30-60 Minuten für Deinen Spaziergang ein",
        "Informiere Dich über interessante Orte auf Deiner Route",
        "Überprüfe das Wetter und wähle passende Kleidung",
        "Nimm eine Wasserflasche mit, besonders bei längeren Spaziergängen"
      ])}

      ${sectionParagraph("Ausrüstung", true)}
      ${sectionBulletList([
        "Bequeme, wetterfeste Kleidung je nach Jahreszeit",
        "Gut passende Schuhe mit ausreichend Dämpfung",
        "Sonnenschutz bei Bedarf (Hut, Sonnenbrille, Sonnencreme)",
        "Wasser zum Trinken",
        "Optional: Schrittzähler oder Fitness-Tracker"
      ])}

      ${sectionParagraph("Tipps für den Spaziergang", true)}
      ${sectionBulletList([
        "Beginne langsam und steigere das Tempo allmählich",
        "Achte auf eine aufrechte Körperhaltung",
        "Atme bewusst und tief durch",
        "Mache kurze Pausen, wenn Du sie brauchst",
        "Genieße die Umgebung und nimm Dir Zeit zum Beobachten"
      ])}

    <div class="content-padding standard-container content-bottom-padding content-top-padding">
        ${infoCardPlain({
          title: "Vorsichtsmaßnahmen",
          text: unsafeHTML(`In den folgenden Fällen solltest Du bitte keine Spaziergang-Übung machen:
          <ul>
            <li>Wenn Du eine Verletzung an Beinen, Knien oder Füßen hast.</li>
            <li>Wenn Du bei der Übung Schmerzen verspürst.</li>
            <li>Bei extremen Wetterbedingungen wie Gewitter oder Glatteis.</li>
            <li>Bei Herz-Kreislauf-Erkrankungen ohne ärztliche Freigabe.</li>
          </ul>
          <p><strong>Hinweise:</strong> Trage geeignete Schuhe und achte auf ausreichend Flüssigkeitszufuhr. Bei längeren Spaziergängen solltest Du eine Pause einlegen, wenn Du Dich erschöpft fühlst.</p>`),
          textColor: "white",
          backgroundColor: "var(--info-card-background-red)"
        })}
    </div>
    <div class="content-padding standard-container content-bottom-padding">
      ${hasActiveHealthGoal ? buttonStandard({
        text: "Weiter",
        variant: "primary"
      }) : ''}
    </div>
  `;
};

console.log("Training Spazieren gehen loaded");
