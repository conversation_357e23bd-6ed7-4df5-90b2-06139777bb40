import { html } from "lit-html";
import { unsafeHTML } from "lit-html/directives/unsafe-html.js";
import { topMenu } from "../../../webcomponents/topMenu.js";
import iconArrowLeft from "../../../svg/icons/ArrowLeft.svg";
import iconDotMenu from "../../../svg/icons/PointsVertical.svg";
import iconSmartphoneTracker from "../../../svg/icons/icon_smartphoneTracker.svg";
import { exitWithSlide } from "../../../router.js";
import { sectionTitle } from "../../../webcomponents/sectionTitle.js";
import { sectionSubtitle } from "../../../webcomponents/sectionSubtitle.js";
import { infoCardPlain } from "../../../webcomponents/infoCardPlain.js";
import { sectionParagraph } from "../../../webcomponents/sectionParagraph.js";
import { sectionBulletList } from "../../../webcomponents/sectionBulletList.js";
import { titleDuoColor } from "../../../webcomponents/titleDuoColor.js";
import imgLockereWanderung from "../../../img/healthgoals/trainings/tr_lockereWanderung.jpg";
import { buttonStandard } from "../../../webcomponents/buttonStandard.js";
import { appStorage } from "../../../utils.js";

/**
 * Top Menu Template mit Slide-Out-Animation beim Zurück-Button
 * @returns {TemplateResult} Das Top-Menu Template
 */
const templateTopMenu = () => {
  // Zurück zur Challenge-Seite
  const backTarget = "/challenge/lockere-wanderung";

  return html`
    ${topMenu({
      backIcon: iconArrowLeft,
      primaryIcon: "",
      menuIcon: iconDotMenu,
      onBack: () => exitWithSlide(backTarget)
    })}
  `;
};

/**
 * Template für die "Lockere Wanderung" Trainings-Seite
 * @returns {TemplateResult} Das Trainings-Seiten Template
 */
export const templateLockereWanderung = () => {
  // Prüfen, ob ein aktives Gesundheitsziel vorhanden ist
  const hasActiveHealthGoal = appStorage._data.activeHealthGoals &&
    (appStorage._data.activeHealthGoals.fitUmgebung ||
     Object.values(appStorage._data.activeHealthGoals).some(value => value === true));

  return html`
    ${templateTopMenu()}
    <div class="content-left-align content-top-padding">
      <div class="content-padding center-text content-bottom-padding">
        ${titleDuoColor("Lockere Wanderung", "", "h1")}
        ${sectionSubtitle("7 bis 10 km", "dark-grn-text", true)}
        <div class="standard-container">
          ${infoCardPlain({
            icon: iconSmartphoneTracker,
            text: "Bitte starte eine Aktivität auf Deinem Fitness-Armband oder in Deiner Fitness-App, falls es das nicht automatisch macht.",
            backgroundColor: "var(--info-card-background-yellow)"
          })}
        </div>
      </div>
    </div>
    <img src="${imgLockereWanderung}" alt="Training Lockere Wanderung">
    <div class="content-padding standard-container content-top-padding">
      ${sectionSubtitle("Details zur Übung", "dark-grn-text")}
    </div>
      ${sectionParagraph(
        "Von einem gemütlichen Spaziergang auf einem flachen Waldweg bis zum leichten Bergsteigen – die positiven gesundheitlichen Effekte nehmen mit der Intensität der körperlichen Aktivität zu. Wegen der großen Bandbreite an Strecken eignet sich Wandern für unterschiedliche Menschen jeden Alters oder auch für Menschen mit körperlichen Einschränkungen wie Adipositas, Diabetes mellitus oder bereits bestehenden Herz-Kreislauf-Erkrankungen."
      )}
      ${sectionParagraph(
        unsafeHTML("Wer in Höhenluft mit großen Steigungen wandert, oder unter hohen Temperaturen, ist am besten vorsichtig. <strong>Vorerkrankte sollten ihren Arzt fragen, welches Pensum für sie geeignet ist.</strong>")
      )}
      ${sectionParagraph(
        "Auch die optimale Ausrüstung ist wichtig. Dazu gehören rückenschonende Rucksäcke mit geeigneter Verpflegung genauso wie ein hinreichender Sonnenschutz und geeignete Kleidung. Ganz wichtig sind die Schuhe. Gute Wanderschuhe sind bei anspruchsvolleren Touren ein Muss und schützen vor Verletzungen."
      )}

      ${sectionParagraph("Vorbereitung und Planung", true)}
      ${sectionBulletList([
        "vorher mit Deinem Arzt oder Deiner Ärztin sprechen",
        "geplante Strecke – entspricht sie Deinen Fähigkeiten?",
        "Langsam beginnen; kleinere Etappen wählen, Streckenlänge allmählich steigern.",
        "Belastung beachten: Beim Wandern musst Du Dich noch gut unterhalten können.",
        "Diabetiker: mit Arzt klären, ob die Insulindosis reduziert werden sollte.",
        "Risiko- und KHK-Patienten und -Patientinnen: Pulsuhr mitnehmen; abbrechen, wenn Du Dich nicht gut fühlst.",
        "vorher prüfen, wie weit beziehungsweise bis auf welche Höhe Du gehen kannst",
        "Wanderungen im Hochgebirge vorher unbedingt mit Arzt besprechen."
      ])}

      ${sectionParagraph("Kleidung und Schuhe", true)}
      ${sectionBulletList([
        "atmungsaktive, nicht beengende Kleidung",
        "wasserdichte Jacke und Wechsel-T-Shirt",
        "leichte, atmungsaktive Kappe oder Mütze gegen Sonne und Regen",
        "Strümpfe, deren Nähte nicht drücken (am besten Kniestrümpfe, an Fersen und Ballen verstärkt)",
        "gut passende Schuhe mit Dämpfungselementen, wasserfest und atmungsaktiv, mit hohem Schaft zur Stabilisierung",
        "leichter, wasserdichter, passgenauer Rucksack"
      ])}

      ${sectionParagraph("Proviant", true)}
      ${sectionBulletList([
        "ausreichend Flüssigkeit – mindestens zwei Liter Wasser pro Person",
        "Herzschwäche-Patienten und -Patientinnen: Trinkmenge sicherheitshalber mit dem Arzt abklären",
        "ausreichend Proviant, zum Beispiel belegte Vollkorn-Brote",
        "Müsliriegel oder Ähnliches für zusätzliche Energie, falls man einmal länger unterwegs ist als geplant",
        "Diabetiker: Traubenzucker oder Fruchtsaftgetränk"
      ])}

      ${sectionParagraph("Notfallvorsorge", true)}
      ${sectionBulletList([
        "Umgebungsplan",
        "eventuell. GPS-fähiges Handy (Achtung: nicht überall Empfang)",
        "geplanten Tourenverlauf Angehörigen mitteilen",
        "Blasenpflaster und kleines Erste-Hilfe-Set",
        "Diabetiker: Blutzuckermessgerät",
        "KHK-Patienten und -Patientinnen: Notfall-Medikament"
      ])}
    <div class="content-padding standard-container content-bottom-padding content-top-padding">
        ${infoCardPlain({
          title: "Vorsichtsmaßnahmen",
          text: unsafeHTML(`In den folgenden Fällen solltest Du bitte keine Wanderung machen:
          <ul>
            <li>Wenn Du eine Verletzung an Beinen, Knien oder Füßen hast.</li>
            <li>Wenn Du bei der Übung Schmerzen verspürst.</li>
            <li>Wenn Du unter Schwindel oder Gleichgewichtsstörungen leidest.</li>
            <li>Bei Herz-Kreislauf-Erkrankungen ohne ärztliche Freigabe.</li>
          </ul>
          <p><strong>Hinweise:</strong> Trage geeignete Wanderschuhe und achte auf ausreichend Flüssigkeitszufuhr. Informiere Dich vorab über die Strecke und nimm ein Mobiltelefon mit.</p>`),
          textColor: "white",
          backgroundColor: "var(--info-card-background-red)"
        })}
    </div>
    <div class="content-padding standard-container content-bottom-padding">
      ${hasActiveHealthGoal ? buttonStandard({
        text: "Weiter",
        variant: "primary"
      }) : ''}
    </div>
  `;
};
