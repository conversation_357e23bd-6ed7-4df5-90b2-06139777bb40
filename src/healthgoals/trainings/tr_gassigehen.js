import { html } from "lit-html";
import { unsafeHTML } from "lit-html/directives/unsafe-html.js";
import { topMenu } from "../../../webcomponents/topMenu.js";
import iconArrowLeft from "../../../svg/icons/ArrowLeft.svg";
import iconDotMenu from "../../../svg/icons/PointsVertical.svg";
import iconSmartphoneTracker from "../../../svg/icons/icon_smartphoneTracker.svg";
import { router, exitWithSlide } from "../../../router.js";
import { sectionTitle } from "../../../webcomponents/sectionTitle.js";
import { sectionSubtitle } from "../../../webcomponents/sectionSubtitle.js";
import { infoCardPlain } from "../../../webcomponents/infoCardPlain.js";
import { sectionParagraph } from "../../../webcomponents/sectionParagraph.js";
import { sectionBulletList } from "../../../webcomponents/sectionBulletList.js";
import { titleDuoColor } from "../../../webcomponents/titleDuoColor.js";
import imgGassigehen from "../../../img/healthgoals/hg_gassiGehen.jpg";
import { buttonStandard } from "../../../webcomponents/buttonStandard.js";
import { appStorage } from "../../../utils.js";

/**
 * Top Menu Template mit Slide-Out-Animation beim Zurück-Button
 * @returns {TemplateResult} Das Top-Menu Template
 */
const templateTopMenu = () => {
  // Zurück zur Challenge-Seite
  const backTarget = "/challenge/gassi-gehen"; 
  
  return html`
    ${topMenu({
      backIcon: iconArrowLeft,
      primaryIcon: "",
      menuIcon: iconDotMenu,
      onBack: () => exitWithSlide(backTarget)
    })}
  `;
};

/**
 * Template für die "Gassigehen" Trainings-Seite
 * @returns {TemplateResult} Das Trainings-Seiten Template
 */
export const templateGassigehen = () => {
  // Prüfen, ob ein aktives Gesundheitsziel vorhanden ist
  const hasActiveHealthGoal = appStorage._data.activeHealthGoals && 
    (appStorage._data.activeHealthGoals.fitUmgebung || 
     Object.values(appStorage._data.activeHealthGoals).some(value => value === true));
  
  return html`
    ${templateTopMenu()}
    <div class="content-left-align content-top-padding">
      <div class="content-padding center-text content-bottom-padding">
        ${titleDuoColor("Gassigehen", "", "h1")}
        ${sectionSubtitle("2 bis 4 km", "dark-grn-text", true)}        
        <div class="standard-container">
          ${infoCardPlain({
            icon: iconSmartphoneTracker,
            text: "Bitte starte eine Aktivität auf Deinem Fitness-Armband oder in Deiner Fitness-App, falls es das nicht automatisch macht.",
            backgroundColor: "var(--info-card-background-yellow)"
          })}
        </div>
      </div>
    </div>
    <img src="${imgGassigehen}" alt="Training Gassigehen">
    <div class="content-padding standard-container content-top-padding">
      ${sectionSubtitle("Details zur Übung", "dark-grn-text")}
    </div>
      ${sectionParagraph(
        "Du möchtest Dich im Freien bewegen, dabei etwas Sinnvolles tun und würdest Dich über Gesellschaft freuen? Wie wäre es mit einem Ehrenamt bei einem Tierheim in Deiner Nähe? Dort warten viele dankbare Fellnasen, die sich über lange Spaziergänge freuen werden."
      )}
      ${sectionParagraph(
        "In der Regel erhältst Du vorab durch das Tierheim eine kostenlose \"Gassigänger\"-Schulung."
      )}
      ${sectionParagraph(
        unsafeHTML("Die Mitarbeitenden in den Tierheimen sind stets bemüht, den Tieren ihren Aufenthalt so angenehm wie möglich zu gestalten. Dazu gehören neben der Bereitstellung von gutem Futter, Wasser, einem schönen Dach über dem Kopf und der Möglichkeit, mit anderen Hunden zu spielen, auch regelmäßige Spaziergänge. Doch nicht alle Tierheime verfügen über die nötigen Ressourcen, die Hunde täglich Gassi zu führen. Auch, wenn Du Dir vielleicht keinen Hund anschaffen kannst, ist schon Dein Einsatz als \"Hunde-Ausführer\" eine absolute Bereicherung:")
      )}
      ${sectionParagraph(
        unsafeHTML("Die Hunde erhalten Streicheleinheiten, sehen etwas von der Welt, bleiben im Kontakt mit Menschen - was wiederum ihre Vermittlungschancen erhöht - und gleichzeitig tust Du etwas für Dein Herz-Kreislauf-System. <strong>Win-Win-Situation für alle!</strong>")
      )}

      ${sectionParagraph("Vorbereitung und Planung", true)}
      ${sectionBulletList([
        "Informiere Dich über Tierheime in Deiner Nähe",
        "Frage nach, ob sie Gassigänger suchen und welche Voraussetzungen Du erfüllen musst",
        "Nimm an einer Gassigänger-Schulung teil, falls angeboten",
        "Kläre ab, welche Hunde für Dich geeignet sind (Größe, Temperament, etc.)",
        "Vereinbare regelmäßige Zeiten, an denen Du kommen kannst"
      ])}

      ${sectionParagraph("Ausrüstung", true)}
      ${sectionBulletList([
        "Bequeme, wetterfeste Kleidung je nach Jahreszeit",
        "Feste Schuhe mit gutem Profil",
        "Eventuell Leckerlis (nur nach Absprache mit dem Tierheim)",
        "Wasser für Dich und den Hund bei längeren Spaziergängen",
        "Kotbeutel"
      ])}

      ${sectionParagraph("Tipps für den Spaziergang", true)}
      ${sectionBulletList([
        "Halte Dich an die Anweisungen des Tierheims bezüglich der Route und Dauer",
        "Achte auf die Körpersprache des Hundes",
        "Lass den Hund nur von der Leine, wenn es ausdrücklich erlaubt ist",
        "Vermeide Kontakt mit fremden Hunden, wenn nicht anders abgesprochen",
        "Melde besondere Vorkommnisse immer dem Tierheim"
      ])}

    <div class="content-padding standard-container content-bottom-padding">
        ${infoCardPlain({
          title: "Vorsichtsmaßnahmen",
          text: unsafeHTML(`In den folgenden Fällen solltest Du bitte keine Gassigeh-Übung machen:
          <ul>
            <li>Wenn Du eine Verletzung an Beinen, Knien oder Füßen hast.</li>
            <li>Wenn Du Angst vor Hunden hast oder allergisch auf sie reagierst.</li>
            <li>Wenn Du bei der Übung Schmerzen verspürst.</li>
            <li>Bei Herz-Kreislauf-Erkrankungen ohne ärztliche Freigabe.</li>
          </ul>
          <p><strong>Hinweise:</strong> Trage geeignete Schuhe und achte auf ausreichend Flüssigkeitszufuhr. Informiere Dich vorab über den Hund, den Du ausführen wirst, und seine besonderen Bedürfnisse.</p>`),
          textColor: "white",
          backgroundColor: "var(--info-card-background-red)"
        })}
    </div>
    <div class="content-padding standard-container content-bottom-padding">
      ${hasActiveHealthGoal ? buttonStandard({
        text: "Weiter",
        variant: "primary"
      }) : ''}
    </div>
  `;
};
