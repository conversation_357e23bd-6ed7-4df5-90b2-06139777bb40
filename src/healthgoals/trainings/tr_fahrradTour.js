import { html } from "lit-html";
import { unsafeHTML } from "lit-html/directives/unsafe-html.js";
import { topMenu } from "../../../webcomponents/topMenu.js";
import iconArrowLeft from "../../../svg/icons/ArrowLeft.svg";
import iconDotMenu from "../../../svg/icons/PointsVertical.svg";
import iconSmartphoneTracker from "../../../svg/icons/icon_smartphoneTracker.svg";
import { router, exitWithSlide } from "../../../router.js";
import { sectionTitle } from "../../../webcomponents/sectionTitle.js";
import { sectionSubtitle } from "../../../webcomponents/sectionSubtitle.js";
import { infoCardPlain } from "../../../webcomponents/infoCardPlain.js";
import { sectionParagraph } from "../../../webcomponents/sectionParagraph.js";
import { sectionBulletList } from "../../../webcomponents/sectionBulletList.js";
import { titleDuoColor } from "../../../webcomponents/titleDuoColor.js";
import imgFahrradTour from "../../../img/healthgoals/hg_fahrradTour.jpg";
import { buttonStandard } from "../../../webcomponents/buttonStandard.js";
import { appStorage } from "../../../utils.js";

/**
 * Top Menu Template mit Slide-Out-Animation beim Zurück-Button
 * @returns {TemplateResult} Das Top-Menu Template
 */
const templateTopMenu = () => {
  // Zurück zur Challenge-Seite
  const backTarget = "/challenge/fahrrad-tour"; 
  
  return html`
    ${topMenu({
      backIcon: iconArrowLeft,
      primaryIcon: "",
      menuIcon: iconDotMenu,
      onBack: () => exitWithSlide(backTarget)
    })}
  `;
};

/**
 * Template für die "Fahrrad-Tour" Trainings-Seite
 * @returns {TemplateResult} Das Trainings-Seiten Template
 */
export const templateFahrradTour = () => {
  // Prüfen, ob ein aktives Gesundheitsziel vorhanden ist
  const hasActiveHealthGoal = appStorage._data.activeHealthGoals && 
    (appStorage._data.activeHealthGoals.fitUmgebung || 
     Object.values(appStorage._data.activeHealthGoals).some(value => value === true));
  
  return html`
    ${templateTopMenu()}
    <div class="content-left-align content-top-padding">
      <div class="content-padding center-text content-bottom-padding">
        ${titleDuoColor("Fahrrad-Tour", "", "h1")}
        ${sectionSubtitle("5 bis 10 km", "dark-grn-text", true)}        
        <div class="standard-container">
          ${infoCardPlain({
            icon: iconSmartphoneTracker,
            text: "Bitte starte eine Aktivität auf Deinem Fitness-Armband oder in Deiner Fitness-App, falls es das nicht automatisch macht.",
            backgroundColor: "var(--info-card-background-yellow)"
          })}
        </div>
      </div>
    </div>
    <img src="${imgFahrradTour}" alt="Training Fahrrad-Tour">
    <div class="content-padding standard-container content-top-padding">
      ${sectionSubtitle("Details zur Übung", "dark-grn-text")}
    </div>
      ${sectionParagraph(
        "Entdecke Deine Umgebung auf dem Fahrrad! Radfahren ist ein echter Kalorienkiller, bringt Dein Herz-Kreislauf-System in Schwung und bietet Dir die perfekte Gelegenheit für spannende Touren."
      )}
      ${sectionParagraph(
        "Achte beim Radeln darauf, einen eher leichten Gang einzulegen und kontinuierlich zu treten. Das ist effektiver für Deine Fitness und schont die Gelenke."
      )}
      ${sectionParagraph(
        unsafeHTML("Vermeide hingegen, mit schweren Gängen Geschwindigkeit aufzubauen, wodurch Du dann potentiell dazu neigen könntest, ungleichmäßiger in die Pedale zu treten.")
      )}

      ${sectionParagraph("Vorbereitung und Planung", true)}
      ${sectionBulletList([
        "Überprüfe Dein Fahrrad vor der Tour (Bremsen, Reifen, Kette)",
        "Plane Deine Route im Voraus und berücksichtige Dein Fitnesslevel",
        "Informiere Dich über Fahrradwege und verkehrsarme Strecken",
        "Beachte die Wettervorhersage und plane entsprechend",
        "Nimm genügend Wasser und eventuell einen kleinen Snack mit"
      ])}

      ${sectionParagraph("Ausrüstung", true)}
      ${sectionBulletList([
        "Fahrradhelm (unbedingt empfohlen für Deine Sicherheit)",
        "Bequeme, atmungsaktive Kleidung",
        "Fahrradhandschuhe für besseren Grip und Komfort",
        "Wasserflasche oder Trinkrucksack",
        "Kleine Reparaturausrüstung (Ersatzschlauch, Multitool)",
        "Handy für Notfälle und Navigation"
      ])}

      ${sectionParagraph("Tipps für die Tour", true)}
      ${sectionBulletList([
        "Starte langsam und steigere Dich allmählich",
        "Achte auf die richtige Sitzhöhe (Bein sollte fast durchgestreckt sein, wenn das Pedal unten ist)",
        "Halte Dich an Verkehrsregeln und fahre defensiv",
        "Mache regelmäßige Pausen, besonders bei längeren Touren",
        "Höre auf Deinen Körper und überfordere Dich nicht"
      ])}

    <div class="content-padding standard-container content-bottom-padding content-top-padding">
        ${infoCardPlain({
          title: "Vorsichtsmaßnahmen",
          text: unsafeHTML(`In den folgenden Fällen solltest Du bitte keine Fahrrad-Tour machen:
          <ul>
            <li>Bei akuten Gelenkschmerzen oder Rückenproblemen</li>
            <li>Bei Gleichgewichtsstörungen oder Schwindel</li>
            <li>Bei Fieber oder akuten Erkrankungen</li>
            <li>Bei Herz-Kreislauf-Erkrankungen ohne ärztliche Freigabe</li>
          </ul>
          <p><strong>Hinweise:</strong> Trage immer einen Helm und achte auf ausreichend Flüssigkeitszufuhr. Fahre nur auf sicheren Wegen und beachte die Verkehrsregeln.</p>`),
          textColor: "white",
          backgroundColor: "var(--info-card-background-red)"
        })}
    </div>
    <div class="content-padding standard-container content-bottom-padding">
      ${hasActiveHealthGoal ? buttonStandard({
        text: "Weiter",
        variant: "primary"
      }) : ''}
    </div>
  `;
};
